<template>
  <view class="assessment-detail">
    <!-- Banner -->
    <image class="banner" :src="detailData.coverImage || defaultImage" mode="aspectFill"></image>

    <!-- 主内容区域 -->
    <scroll-view scroll-y class="detail-content">
      <!-- 头部信息 -->
      <view class="header-info">
        <view class="title-row">
          <text class="title">{{detailData.name}}</text>
          <view class="tag professional" v-if="detailData.payMode === 1">付费版</view>
          <view class="tag free" v-else>免费版</view>
        </view>
        <view class="stats-row">
          <view class="left-stats">
            <text>{{detailData.questionCount}}道题</text>
            <text class="dot">·</text>
            <text>{{detailData.duration}}</text>
            <text class="dot">·</text>
            <text>{{detailData.userTestCount || 0}}人测过</text>
          </view>
          <view class="right-stats">
            <text>{{detailData.viewCount || 0}}人浏览</text>
          </view>
        </view>
        <!-- 价格信息 -->
        <view class="price-row" v-if="detailData.payMode === 1">
          <text class="current-price">¥{{detailData.currentPrice}}</text>
          <text class="original-price" v-if="detailData.originalPrice > detailData.currentPrice">¥{{detailData.originalPrice}}</text>
        </view>
      </view>

      <!-- 测评介绍内容 -->
      <view class="intro-content">
        <view class="intro-header">
          <text class="section-title">测评介绍</text>
          <view class="right-links">
            <text v-if="detailData.testNotice" class="link-text" @click="showNoticePopup">测评须知</text>
            <text v-if="detailData.testNotice && detailData.referenceLiterature" class="divider">|</text>
            <text v-if="detailData.referenceLiterature" class="link-text" @click="showReferencePopup">引用文献</text>
          </view>
        </view>
        <text class="section-content">{{detailData.description || detailData.instruction || detailData.introduction}}</text>

        <view v-if="detailData.testTheory" class="section">
          <text class="section-title">测评基础理论</text>
          <text class="section-content">{{detailData.testTheory}}</text>
        </view>

        <view v-if="detailData.testPurpose" class="section">
          <text class="section-title">测评目的</text>
          <text class="section-content">{{detailData.testPurpose}}</text>
        </view>

        <view v-if="detailData.testApplication" class="section">
          <text class="section-title">测评应用</text>
          <text class="section-content">{{detailData.testApplication}}</text>
        </view>

        <view v-if="detailData.testObject" class="section">
          <text class="section-title">测评对象</text>
          <text class="section-content">{{detailData.testObject}}</text>
        </view>

        <view v-if="detailData.testPreparation" class="section">
          <text class="section-title">测评准备</text>
          <text class="section-content">{{detailData.testPreparation}}</text>
        </view>

        <view v-if="detailData.testProcessing" class="section">
          <text class="section-title">测评后的处理</text>
          <text class="section-content">{{detailData.testProcessing}}</text>
        </view>

        <view v-if="detailData.testAttention" class="section">
          <text class="section-title">注意事项</text>
          <text class="section-content">{{detailData.testAttention}}</text>
        </view>

        <view class="section">
          <text class="section-title">用户评价</text>
          <view class="reviews-summary" v-if="detailData.ratingAvg">
            <view class="rating-info">
              <text class="rating-score">{{detailData.ratingAvg}}</text>
              <view class="stars">
                <text class="star" v-for="i in 5" :key="i" :class="{'filled': i <= Math.floor(detailData.ratingAvg)}">★</text>
              </view>
              <text class="review-count">{{detailData.reviewCount || 0}}条评价</text>
            </view>
          </view>
          <view class="reviews-list" v-if="reviews.length > 0">
            <view class="review-item" v-for="(review, index) in reviews.slice(0, 3)" :key="index">
              <view class="review-header">
                <text class="reviewer-name">{{review.userName || '匿名用户'}}</text>
                <view class="review-stars">
                  <text class="star" v-for="i in 5" :key="i" :class="{'filled': i <= review.rating}">★</text>
                </view>
              </view>
              <text class="review-content">{{review.content}}</text>
              <text class="review-time">{{formatTime(review.createTime)}}</text>
            </view>
            <view class="view-more-reviews" @click="viewAllReviews" v-if="reviews.length > 3">
              <text>查看更多评价</text>
            </view>
          </view>
        </view>

        <view class="section">
          <text class="section-title">相关测评</text>
          <view class="related-tests">
            <view class="related-item" v-for="(item, index) in relatedTests" :key="index" @click="viewDetail(item)">
              <image :src="item.imageUrl || defaultImage" mode="aspectFill"></image>
              <text>{{item.name}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部占位 -->
      <view style="height: 120rpx;"></view>
    </scroll-view>

    <!-- 底部导航 -->
    <UniversalGoodsNav
      page-type="assessment"
      :detail-data="detailData"
      :purchased="testRecord && (testRecord.statusDesc === '已完成' || testRecord.completed === true)"
      :assessment-status="testRecord?.statusDesc || ''"
      :price="detailData.price"
      :favorited="detailData.favorited"
      :favorite-id="detailData.favoriteId"
      @favorite="handleFavorite"
      @contact-service="handleContactService"
      @share="handleShare"
      @main-action="handleMainAction"
    />

    <!-- 测评须知弹框 -->
    <uni-popup ref="noticePopup" type="bottom">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">测评须知</text>
          <text class="close-btn" @click="closeNoticePopup">×</text>
        </view>
        <scroll-view scroll-y class="popup-body">
          <text class="popup-text">{{detailData.testNotice}}</text>
        </scroll-view>
      </view>
    </uni-popup>

    <!-- 引用文献弹框 -->
    <uni-popup ref="referencePopup" type="bottom">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">引用文献</text>
          <text class="close-btn" @click="closeReferencePopup">×</text>
        </view>
        <scroll-view scroll-y class="popup-body">
          <text class="popup-text">{{detailData.referenceLiterature}}</text>
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, getCurrentInstance, onUnmounted } from 'vue'
import { onLoad } from "@dcloudio/uni-app"
import {
  getAssessment,
  getAssessmentRecords,
  getAssessmentProgress,
  getScaleReviews,
  startAssessment as startAssessmentApi,
  checkCanStartAssessment,
  getRecommendedAssessments
} from '@/api/evaluation'
import { formatTime, parseCompatibleDate } from '@/utils/index'
import UniversalGoodsNav from '@/components/UniversalGoodsNav/UniversalGoodsNav.vue'
import { useUserStore } from '@/stores/user'

const detailData = ref({})
const testRecord = ref(null)
const assessmentId = ref('')
const recordId = ref('')
const noticePopup = ref(null)
const referencePopup = ref(null)
const defaultImage = '/static/evaluation/default.png'
const relatedTests = ref([])
const isFavorite = ref(false)
const currentTab = ref('intro')
const reviews = ref([])
const reviewsLoading = ref(false)
const userStore = useUserStore()

// 处理收藏事件
const handleFavorite = (favoriteData) => {
  detailData.value.favorited = favoriteData.favorited
  detailData.value.favoriteId = favoriteData.favoriteId
}

// 处理客服事件
const handleContactService = () => {
  // 联系客服逻辑
}

// 处理分享事件
const handleShare = (shareConfig) => {
  uni.showToast({
    title: '转发成功',
    icon: 'success'
  })
}

// 处理主要操作事件
const handleMainAction = ({ pageType }) => {
  if (testRecord.value) {
    if (testRecord.value.statusDesc === '已完成' || testRecord.value.completed === true) {
      viewReport()
    } else if (testRecord.value.statusDesc === '进行中' || testRecord.value.statusDesc === '暂停中') {
      continueAssessment()
    } else {
      startAssessment()
    }
  } else {
    startAssessment()
  }
}

// 显示测评须知弹框
const showNoticePopup = () => {
  noticePopup.value.open()
}

// 关闭测评须知弹框
const closeNoticePopup = () => {
  noticePopup.value.close()
}

// 显示引用文献弹框
const showReferencePopup = () => {
  referencePopup.value.open()
}

// 关闭引用文献弹框
const closeReferencePopup = () => {
  referencePopup.value.close()
}



// 查看详情
const viewDetail = (item) => {
  uni.navigateTo({
    url: `/pages/evaluation/detail/index?id=${item.id}`
  })
}

// 获取评价数据
const getReviews = async () => {
  if (reviewsLoading.value) return
  reviewsLoading.value = true

  try {
    const res = await getScaleReviews(assessmentId.value, { pageNum: 1, pageSize: 10 })
    if (res.code === 200) {
      reviews.value = res.data || []
    }
  } catch (error) {
    console.error('获取评价失败:', error)
  } finally {
    reviewsLoading.value = false
  }
}

// 查看所有评价
const viewAllReviews = () => {
  uni.navigateTo({
    url: `/pages/evaluation/reviews/index?scaleId=${assessmentId.value}&scaleName=${encodeURIComponent(detailData.value.title)}`
  })
}

onLoad(async (options) => {
  try {
    assessmentId.value = options.id

    // 获取测评详情
    const res = await getAssessment(options.id)

    if (res.code === 200) {
      detailData.value = res.data

      uni.setNavigationBarTitle({
        title: res.data.name
      })

      // 获取推荐的相似测评
      try {
        const recommendRes = await getRecommendedAssessments(assessmentId.value, 5)
        if (recommendRes.code === 200) {
          relatedTests.value = recommendRes.data || []
        }
      } catch (error) {
        console.error('获取推荐测评失败:', error)
      }
    } else {
      console.error('获取测评详情失败:', res)
      uni.showToast({
        title: res.msg || '获取测评详情失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('加载测评详情异常:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }

  // 获取评价数据
  await getReviews()

  // 检查用户是否有该量表的测评记录
  await checkUserTestRecord()

  // 如果有recordId，获取特定的测评记录信息
  if (options.recordId) {
    recordId.value = options.recordId
    await getTestRecordInfo()
  }

  // 更新底部按钮文字
  updateButtonText()
})

// 检查用户是否有该量表的测评记录
const checkUserTestRecord = async () => {
  try {
    // 获取用户所有测评记录
    const res = await getAssessmentRecords()

    if (res.code === 200 && res.data && res.data.length > 0) {
      // 查找该量表的测评记录
      const scaleRecords = res.data.filter(item => item.scaleId == assessmentId.value)

      if (scaleRecords.length > 0) {
        // 按创建时间排序，获取最新的记录
        scaleRecords.sort((a, b) => {
          const dateA = parseCompatibleDate(a.createTime)
          const dateB = parseCompatibleDate(b.createTime)
          return dateB - dateA
        })
        const latestRecord = scaleRecords[0]

        recordId.value = latestRecord.id

        // 获取详细的测评进度信息
        await getDetailedProgress(latestRecord.id)

        return
      }
    }
  } catch (error) {
    console.error('检查用户测评记录失败:', error)
  }
}

// 获取详细的测评进度信息
const getDetailedProgress = async (recordId) => {
  try {
    const progressRes = await getAssessmentProgress(recordId)

    if (progressRes.code === 200 && progressRes.data) {
      const progressData = progressRes.data

      // 构造兼容的测评记录对象
      testRecord.value = {
        id: progressData.record_id,
        recordId: progressData.record_id,
        scaleId: progressData.scale_id,
        userId: progressData.user_id,
        startTime: progressData.start_time,
        status: progressData.status,
        statusDesc: progressData.status_text,
        completed: progressData.status_text === '已完成',
        progress: progressData.actual_progress,
        currentQuestionNo: progressData.current_question_no,
        answeredQuestions: progressData.answered_questions,
        totalQuestions: progressData.total_questions,
        remainingQuestions: progressData.remaining_questions
      }
    }
  } catch (error) {
    console.error('获取测评进度失败:', error)
  }
}

// 更新按钮文字
const updateButtonText = () => {
  // 实际的按钮文字在 UniversalGoodsNav 组件中处理
}

// 获取测评记录信息
const getTestRecordInfo = async () => {
  try {
    // 优先使用新的API获取量表相关记录
    const res = await getAssessmentHistory(assessmentId.value)
    if (res.code === 200 && res.data && res.data.length > 0) {
      const record = res.data.find(item => item.id == recordId.value || item.recordId == recordId.value)
      if (record) {
        testRecord.value = record
        return
      }
    }

    // 如果没找到，尝试获取所有记录
    const allRes = await getAssessmentRecords()
    if (allRes.code === 200 && allRes.data && allRes.data.length > 0) {
      const record = allRes.data.find(item =>
        (item.id == recordId.value || item.recordId == recordId.value) &&
        item.scaleId == assessmentId.value
      )
      if (record) {
        testRecord.value = record
        return
      }
    }

    // 兼容旧版本API
    const legacyRes = await getTestRecords()
    if (legacyRes.code === 200) {
      const record = legacyRes.data.find(item => item.recordId == recordId.value)
      if (record) {
        testRecord.value = record
      }
    }
  } catch (error) {
    console.error('获取测评记录失败:', error)
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    0: 'status-pending',
    1: 'status-completed'
  }
  return classMap[status] || 'status-pending'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    0: '进行中',
    1: '已完成'
  }
  return textMap[status] || '未开始'
}

// 开始测评
const startAssessment = async () => {
  try {
    // 检查是否可以开始测评
    const checkRes = await checkCanStartAssessment(assessmentId.value)
    if (checkRes.code !== 200 || !checkRes.data.canAssess) {
      uni.showToast({
        title: checkRes.data.errors || '无法开始测评',
        icon: 'none'
      })
      return
    }

    // 开始测评
    const res = await startAssessmentApi(assessmentId.value)
    if (res.code === 200) {
      const recordId = res.data
      const title = encodeURIComponent(detailData.value.name)
      uni.navigateTo({
        url: `/pages/evaluation/answer/index?id=${assessmentId.value}&recordId=${recordId}&title=${title}`
      })
    } else {
      uni.showToast({
        title: res.msg || '开始测评失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('开始测评失败:', error)
    uni.showToast({
      title: '开始测评失败',
      icon: 'none'
    })
  }
}

// 继续测评
const continueAssessment = () => {
  const title = encodeURIComponent(detailData.value.name)

  if (recordId.value) {
    uni.navigateTo({
      url: `/pages/evaluation/answer/index?id=${assessmentId.value}&recordId=${recordId.value}&title=${title}`
    })
  } else {
    // 如果没有recordId，当作新测评开始
    console.warn('没有找到recordId，将开始新测评')
    startAssessment()
  }
}

// 查看报告
const viewReport = () => {
  const title = encodeURIComponent(detailData.value.name)

  if (recordId.value) {
    uni.navigateTo({
      url: `/pages/evaluation/report/index?recordId=${recordId.value}&title=${title}`
    })
  } else {
    uni.showToast({
      title: '无法找到测评记录',
      icon: 'none'
    })
  }
}

// 添加转发支持

const shareConfig = ref(null)

// 监听转发事件
uni.$on('triggerShare', (config) => {
  shareConfig.value = config
  // #ifdef MP-WEIXIN
  uni.showToast({
    title: '请点击右上角分享',
    icon: 'none'
  })
  // #endif
})

// 页面卸载时移除监听
onUnmounted(() => {
  uni.$off('triggerShare')
})
</script>

<script>
// 支持转发的页面配置
export default {
  onShareAppMessage() {
    const currentInstance = getCurrentInstance()
    const shareConfig = currentInstance?.ctx?.shareConfig

    if (shareConfig) {
      return {
        title: shareConfig.title,
        path: shareConfig.path,
        imageUrl: shareConfig.imageUrl
      }
    }

    // 默认分享配置
    const detailData = currentInstance?.ctx?.detailData
    if (detailData) {
      return {
        title: `推荐测评：${detailData.scaleName || detailData.name || detailData.title}`,
        path: `pages/evaluation/detail/index?id=${detailData.id}`,
        imageUrl: detailData.imageUrl
      }
    }

    return {
      title: '熙桓心理测评',
      path: 'pages/index/index'
    }
  },

  onShareTimeline() {
    const currentInstance = getCurrentInstance()
    const shareConfig = currentInstance?.ctx?.shareConfig

    if (shareConfig) {
      return {
        title: shareConfig.title,
        query: '',
        imageUrl: shareConfig.imageUrl
      }
    }

    // 默认分享配置
    const detailData = currentInstance?.ctx?.detailData
    if (detailData) {
      return {
        title: `推荐测评：${detailData.scaleName || detailData.name || detailData.title}`,
        query: `id=${detailData.id}`,
        imageUrl: detailData.imageUrl
      }
    }

    return {
      title: '熙桓心理测评'
    }
  }
}
</script>

<style lang="scss">
page {
  background-color: #f5f5f5;
}

.assessment-detail {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;

  .banner {
    width: 100%;
    height: 400rpx;
  }

  .detail-content {
    flex: 1;
    
    .header-info {
      background: #fff;
      padding: 30rpx;
      
      .title-row {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        
        .title {
          flex: 1;
          font-size: 36rpx;
          font-weight: bold;
          color: #333;
        }
        
        .tag {
          padding: 4rpx 16rpx;
          border-radius: 4rpx;
          font-size: 24rpx;

          &.professional {
            background: #e6f7ff;
            color: #1890ff;
          }

          &.free {
            background: #f6ffed;
            color: #52c41a;
          }
        }
      }
      
      .stats-row {
        display: flex;
        justify-content: space-between;
        font-size: 28rpx;
        color: #666;
        
        .left-stats {
          .dot {
            margin: 0 12rpx;
            color: #999;
          }
        }
        
        .right-stats {
          color: #ff6b6b;
        }
      }

      .price-row {
        margin-top: 20rpx;
        display: flex;
        align-items: center;
        gap: 20rpx;

        .current-price {
          font-size: 32rpx;
          font-weight: bold;
          color: #ff4d4f;
        }

        .original-price {
          font-size: 24rpx;
          color: #999;
          text-decoration: line-through;
        }
      }
    }

    .tabs {
      display: flex;
      background: #fff;
      padding: 0 30rpx;
      margin-top: 20rpx;
      border-bottom: 1rpx solid #eee;
      
      .tab-item {
        position: relative;
        padding: 24rpx 40rpx;
        
        text {
          font-size: 28rpx;
          color: #666;
        }
        
        &.active {
          text {
            color: #333;
            font-weight: bold;
          }
          
          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 48rpx;
            height: 4rpx;
            background: #409eff;
            border-radius: 2rpx;
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      background: #fff;
      padding: 20rpx 0;
      margin-top: 20rpx;
      
      .action-item {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        
        &:not(:last-child)::after {
          content: '';
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 1rpx;
          height: 24rpx;
          background: #eee;
        }
        
        text {
          font-size: 28rpx;
          color: #666;
          font-weight: normal;
          
          &.active {
            color: #333;
            font-weight: bold;
          }
        }
      }
    }

    .intro-content {
      background: #fff;
      margin-top: 20rpx;
      padding: 30rpx;
      
      .intro-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;
        
        .section-title {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
        }
        
        .right-links {
          display: flex;
          align-items: center;
          
          .link-text {
            font-size: 26rpx;
            color: #999;
            padding: 0 12rpx;
          }
          
          .divider {
            font-size: 24rpx;
            color: #ddd;
          }
        }
      }
      
      .section-content {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
        margin-bottom: 40rpx;
        display: block;
      }

      .section {
        margin-bottom: 40rpx;
        
        .section-title {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 20rpx;
          display: block;
        }
        
        .section-content {
          font-size: 28rpx;
          color: #666;
          line-height: 1.6;
        }

        .related-tests {
          display: flex;
          flex-wrap: wrap;
          margin: 0 -10rpx;
          
          .related-item {
            width: calc(50% - 20rpx);
            margin: 10rpx;
            
            image {
              width: 100%;
              height: 200rpx;
              border-radius: 12rpx;
              margin-bottom: 10rpx;
            }
            
            text {
              font-size: 28rpx;
              color: #333;
              display: block;
            }
          }
        }
      }
    }
  }

  .footer-goods-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }
}

.popup-content {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  min-height: 60vh;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  
  .popup-header {
    padding: 30rpx;
    border-bottom: 1rpx solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .popup-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
    
    .close-btn {
      font-size: 40rpx;
      color: #999;
      padding: 10rpx;
    }
  }
  
  .popup-body {
    flex: 1;
    padding: 30rpx;
    
    .popup-text {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
    }
  }
}

// 评价相关样式
.reviews-summary {
  margin-bottom: 30rpx;

  .rating-info {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .rating-score {
      font-size: 48rpx;
      font-weight: bold;
      color: #ff6b35;
    }

    .stars {
      display: flex;
      gap: 4rpx;

      .star {
        font-size: 24rpx;
        color: #ddd;

        &.filled {
          color: #ff6b35;
        }
      }
    }

    .review-count {
      font-size: 26rpx;
      color: #666;
    }
  }
}

.reviews-list {
  .review-item {
    padding: 24rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .review-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;

      .reviewer-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }

      .review-stars {
        display: flex;
        gap: 2rpx;

        .star {
          font-size: 20rpx;
          color: #ddd;

          &.filled {
            color: #ff6b35;
          }
        }
      }
    }

    .review-content {
      font-size: 26rpx;
      color: #666;
      line-height: 1.5;
      margin-bottom: 8rpx;
    }

    .review-time {
      font-size: 24rpx;
      color: #999;
    }
  }

  .view-more-reviews {
    text-align: center;
    padding: 20rpx 0;
    color: #409eff;
    font-size: 26rpx;
  }
}
</style>